import 'dart:async';
import 'package:injectable/injectable.dart';
import 'package:notifications/domain/facade/scheduled_notifications_facade.dart';
import '../datasources/local_telemetry_datasource.dart';
import '../../domain/models/therapy_session_model.dart';

@LazySingleton()
class TherapyFeedbackTriggerService {
  final ILocalTelemetryDataSource _localDataSource;
  final ScheduledNotificationsFacade _notificationsFacade;

  // Stream controller for feedback triggers
  final StreamController<String> _feedbackTriggerController =
      StreamController<String>.broadcast();

  // Track recently triggered sessions to prevent duplicate triggers
  final Set<String> _recentlyTriggeredSessions = <String>{};
  static const Duration _triggerCooldown = Duration(minutes: 2);

  TherapyFeedbackTriggerService(
    this._localDataSource,
    this._notificationsFacade,
  );

  /// Stream that emits session IDs when feedback should be shown
  Stream<String> get feedbackTriggerStream => _feedbackTriggerController.stream;

  /// Check if notification payload contains a session that needs feedback
  Future<void> checkNotificationPayload(String? payload) async {
    if (payload == null || payload.isEmpty) {
      print('🔍 No payload provided for feedback check');
      return;
    }

    try {
      print('🔍 Checking notification payload for feedback: $payload');

      // Get sessions from local datasource
      final sessions = await _localDataSource.getTherapySessions();

      // Find session with matching ID
      final session =
          sessions.where((s) => s.sessionInfo.sessionId == payload).firstOrNull;

      if (session != null &&
          _sessionNeedsFeedback(session) &&
          !_wasRecentlyTriggered(payload)) {
        print('✅ Session needs feedback, triggering feedback UI: $payload');

        // Cancel the notification since we're showing feedback in foreground
        await _cancelNotification(payload);

        // Mark as recently triggered and trigger feedback UI
        _markAsTriggered(payload);
        _feedbackTriggerController.add(payload);
      } else {
        print('ℹ️ Session does not need feedback: $payload');

        // If session already has feedback completed, cancel any remaining notifications
        if (session != null && session.feedback?.feedbackCompleted == true) {
          print(
              '🚫 Session already has feedback completed, canceling notifications');
          await _cancelNotification(payload);
        }
      }
    } catch (e) {
      print('❌ Error checking notification payload: $e');
    }
  }

  /// Check if most recent session needs feedback (app opened without notification)
  Future<void> checkMostRecentSession() async {
    try {
      print('🔍 Checking most recent session for feedback');

      final sessions = await _localDataSource.getTherapySessions();

      if (sessions.isEmpty) {
        print('ℹ️ No sessions found');
        return;
      }

      // Sort by start time to get the most recent session
      final sortedSessions = List<TherapySessionModel>.from(sessions);
      sortedSessions.sort((a, b) {
        final aTime = a.sessionInfo.therapyStartTime;
        final bTime = b.sessionInfo.therapyStartTime;
        if (bTime == null && aTime == null) return 0;
        if (bTime == null) return -1;
        if (aTime == null) return 1;
        return bTime.compareTo(aTime);
      });

      final mostRecentSession = sortedSessions.first;
      print(
          '🔍 Most recent session: ${mostRecentSession.sessionInfo.sessionId}');

      // Check if session needs to be ended first (disconnection scenario)
      if (await _shouldEndSession(mostRecentSession)) {
        print('🔄 Session needs to be ended due to disconnection');
        await _endAndSyncSession(mostRecentSession);

        // After ending, check if feedback is needed
        if (_sessionNeedsFeedback(mostRecentSession) &&
            !_wasRecentlyTriggered(mostRecentSession.sessionInfo.sessionId)) {
          print('✅ Session ended and needs feedback, triggering feedback UI');
          _markAsTriggered(mostRecentSession.sessionInfo.sessionId);
          _feedbackTriggerController
              .add(mostRecentSession.sessionInfo.sessionId);
          return;
        }
      }

      // Check if session needs feedback
      if (_sessionNeedsFeedback(mostRecentSession) &&
          !_wasRecentlyTriggered(mostRecentSession.sessionInfo.sessionId)) {
        print('✅ Most recent session needs feedback, triggering feedback UI');

        // Cancel any pending notification since we're showing feedback
        await _cancelNotification(mostRecentSession.sessionInfo.sessionId);

        // Mark as triggered and trigger feedback UI
        _markAsTriggered(mostRecentSession.sessionInfo.sessionId);
        _feedbackTriggerController.add(mostRecentSession.sessionInfo.sessionId);
      } else {
        print('ℹ️ Most recent session does not need feedback');
      }
    } catch (e) {
      print('❌ Error checking most recent session: $e');
    }
  }

  /// Check if a session needs feedback
  bool _sessionNeedsFeedback(TherapySessionModel session) {
    // A session needs feedback if:
    // 1. It's completed AND
    // 2. Feedback hasn't been completed yet AND
    // 3. Session actually had some activity (has events or duration > 30 seconds)
    // Note: We don't require feedbackRequested=true to be more lenient with older sessions

    final isCompleted = session.status == 'completed';
    final feedbackNotCompleted = session.feedback?.feedbackCompleted != true;

    // Check if session had meaningful activity
    bool hasActivity = false;
    if (session.sessionInfo.therapyStartTime != null &&
        session.sessionInfo.therapyEndTime != null) {
      final duration = session.sessionInfo.therapyEndTime!
          .difference(session.sessionInfo.therapyStartTime!);
      hasActivity = duration.inSeconds > 30; // At least 30 seconds of activity
    }

    // Also check if session has any events (indicating user interaction)
    final hasEvents = session.sessionEvents.isNotEmpty;

    // Check if session is not too old (within last 24 hours)
    bool isRecent = true;
    if (session.sessionInfo.therapyEndTime != null) {
      final sessionAge =
          DateTime.now().difference(session.sessionInfo.therapyEndTime!);
      isRecent = sessionAge.inHours <
          24; // Only show feedback for sessions within 24 hours
    }

    final needsFeedback = isCompleted &&
        feedbackNotCompleted &&
        (hasActivity || hasEvents) &&
        isRecent;

    print(
        '🔍 Session ${session.sessionInfo.sessionId} needs feedback: $needsFeedback '
        '(completed: $isCompleted, feedbackCompleted: ${session.feedback?.feedbackCompleted}, '
        'hasActivity: $hasActivity, hasEvents: $hasEvents, isRecent: $isRecent, eventCount: ${session.sessionEvents.length})');

    return needsFeedback;
  }

  /// Check if session should be ended (disconnection scenario)
  Future<bool> _shouldEndSession(TherapySessionModel session) async {
    // If session is not completed and has been running for more than expected time
    if (session.status != 'completed' &&
        session.sessionInfo.therapyStartTime != null) {
      final sessionAge =
          DateTime.now().difference(session.sessionInfo.therapyStartTime!);
      // Auto-end if session has been running for more than 5 minutes (therapy auto-end time)
      final shouldEnd = sessionAge.inMinutes > 5;

      if (shouldEnd) {
        print(
            '⏰ Session should be ended - age: ${sessionAge.inMinutes} minutes');
      }

      return shouldEnd;
    }
    return false;
  }

  /// End session and sync to cloud (for disconnection scenarios)
  Future<void> _endAndSyncSession(TherapySessionModel session) async {
    try {
      print(
          '🔄 Ending and syncing disconnected session: ${session.sessionInfo.sessionId}');

      // Update session status to completed
      final now = DateTime.now();
      final updatedSession = session.copyWith(
        status: 'completed',
        sessionInfo: session.sessionInfo.copyWith(
          therapyEndTime: now,
        ),
      );

      // Save updated session locally
      await _localDataSource.updateTherapySession(updatedSession);

      print('✅ Session ended and will be synced to cloud');
    } catch (e) {
      print('❌ Error ending and syncing session: $e');
    }
  }

  /// Cancel notification for session
  Future<void> _cancelNotification(String sessionId) async {
    try {
      // Generate the same notification ID that was used when scheduling
      final notificationId = 'feedback_$sessionId';

      final result =
          await _notificationsFacade.cancelSingleNotification(notificationId);
      result.mapBoth(
        onLeft: (failure) => print('❌ Failed to cancel notification: $failure'),
        onRight: (_) =>
            print('✅ Successfully canceled notification: $notificationId'),
      );
    } catch (e) {
      print('❌ Error canceling notification: $e');
    }
  }

  /// Check if session was recently triggered to prevent duplicates
  bool _wasRecentlyTriggered(String sessionId) {
    return _recentlyTriggeredSessions.contains(sessionId);
  }

  /// Mark session as recently triggered
  void _markAsTriggered(String sessionId) {
    _recentlyTriggeredSessions.add(sessionId);

    // Remove from set after cooldown period
    Timer(_triggerCooldown, () {
      _recentlyTriggeredSessions.remove(sessionId);
    });
  }

  /// Dispose resources
  void dispose() {
    _feedbackTriggerController.close();
  }
}
