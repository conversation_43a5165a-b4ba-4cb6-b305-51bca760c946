import 'dart:convert';
import 'dart:io' as io;
import 'package:account_management/domain/facade/account_management_facade.dart';
import 'package:account_management/domain/facade/period_tracking_facade.dart';
import 'package:account_management/domain/facade/ovulation_facade.dart';
import 'package:account_management/domain/failure/account_management_failures.dart';
import 'package:account_management/domain/model/account_details_model.dart';
import 'package:account_management/domain/model/health_data.dart';
import 'package:account_management/domain/model/period_prediction_metadata.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';
import 'package:injectable/injectable.dart';
import 'package:image_picker/image_picker.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:doso/doso.dart';
import '../domain/core/unit.dart';

@LazySingleton(as: AccountManagementFacade)
class AccountManagementRepository implements AccountManagementFacade {
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final FirebaseAuth _firebaseAuth = FirebaseAuth.instance;
  final PeriodTrackingFacade _periodTrackingFacade;
  final OvulationFacade _ovulationFacade;

  AccountManagementRepository(
      this._periodTrackingFacade, this._ovulationFacade);
  final FirebaseStorage _firebaseStorage = FirebaseStorage.instance;

  @override
  Stream<Do<AccountManagementFailure, AccountDetailsModel>>
      getAccountDetails() async* {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      yield* userDoc.snapshots().map((snapshot) {
        if (snapshot.exists) {
          return Do.success(AccountDetailsModel.fromJson(snapshot.data()!));
        } else {
          return const Do.failure(
              AccountManagementFailure.accountDetailsLoadFailure(
                  'usernotfound'));
        }
      });
    } on FirebaseException catch (e) {
      yield Do.failure(
          AccountManagementFailure.accountDetailsLoadFailure(e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateAccountDetails(
      AccountDetailsModel accountDetails) async {
    try {
      final user = _firebaseAuth.currentUser;
      final userDoc = _firestore.collection('users').doc(user!.uid);
      return userDoc
          .update(accountDetails.toJson())
          .then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateAccountEmail(
      String email) async {
    try {
      final user = _firebaseAuth.currentUser;
      return user!
          .verifyBeforeUpdateEmail(email)
          .then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateAccountPassword(
      String password) async {
    try {
      final user = _firebaseAuth.currentUser;
      return user!.updatePassword(password).then((_) => const Do.success(unit));
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateAccountPhoneNumber(
      String phoneNumber) async {
    try {
      final user = _firebaseAuth.currentUser;
      return Do.success(unit);
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateProfilePicture(
      XFile? profilePicture) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null || profilePicture == null) {
        return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
            'usernotfound'));
      }
      // Step 1: Upload the profile picture
      TaskSnapshot uploadTask = await _firebaseStorage
          .ref('profile_pictures/user/${user.uid}')
          .putFile(io.File(profilePicture.path));

      // Step 2: Get the download URL
      String downloadURL = await uploadTask.ref.getDownloadURL();

      // Step 3: Update the user document with the new picture URL
      await _firestore
          .collection('users')
          .doc(user.uid)
          .update({'photoURL': downloadURL});

      return const Do.success(unit);
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> deleteProfilePicture() async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
            'usernotfound'));
      }
      // Step 1: Delete the profile picture
      await _firebaseStorage.ref('profile_pictures/user/${user.uid}').delete();

      // Step 2: Update the user document with the new picture URL
      await _firestore
          .collection('users')
          .doc(user.uid)
          .update({'photoURL': null});

      return const Do.success(unit);
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  @override
  Future<Do<AccountManagementFailure, Unit>> updateOnboardingData(
      HealthDataModel healthData, DateTime dateOfBirth) async {
    try {
      final user = _firebaseAuth.currentUser;
      final sharedPreferences = await SharedPreferences.getInstance();
      if (user == null) {
        return const Do.failure(
            AccountManagementFailure.accountDetailsUpdateFailure(
                'usernotfound'));
      }

      // Step 1: Calculate future dates based on onboarding data
      final lastPeriodDate = healthData.lastPeriodDate;
      final cycleLength = healthData.cycleLength ?? 28;
      final periodLength = healthData.periodLength ?? 5;

      if (lastPeriodDate != null) {
        // Calculate next period start date and next ovulation date
        final nextPeriodStartDate =
            lastPeriodDate.add(Duration(days: cycleLength));
        final nextOvulationDate =
            lastPeriodDate.add(Duration(days: cycleLength - 14));

        // Create initial prediction metadata
        final initialPredictionMetadata = PeriodPredictionMetadata.initial(
          initialCycleLength: cycleLength.toDouble(),
          initialPeriodLength: periodLength.toDouble(),
          lastPeriodDate: lastPeriodDate,
        );

        // Update health data with calculated future dates and prediction metadata
        healthData = HealthDataModel(
          periodLength: healthData.periodLength,
          cycleLength: healthData.cycleLength,
          contraceptionType: healthData.contraceptionType,
          lastPeriodDate: healthData.lastPeriodDate,
          ovulationDate: healthData.ovulationDate,
          nextPeriodStartDate: nextPeriodStartDate,
          nextOvulationDate: nextOvulationDate,
          periodReminderSettings: healthData.periodReminderSettings,
          predictionMetadata: initialPredictionMetadata,
        );

        // Step 2: Create period tracking entries for the last period
        await _createInitialPeriodTrackingData(
            user.uid, lastPeriodDate, periodLength);

        // Step 2.5: Initialize prediction system with onboarding data
        await _initializePredictionsFromOnboarding(
            lastPeriodDate, cycleLength, periodLength);
      }

      // Step 3: Update the user document with the new health data and onboarding status
      await _firestore.collection('users').doc(user.uid).set({
        'dateOfBirth': Timestamp.fromDate(dateOfBirth),
        'healthData': healthData.toJson(),
        'isOnboarded': true, // Set onboarding status in Firestore
      }, SetOptions(merge: true));

      // Fetch the current list of onboarded user IDs
      final String? onboardedUserIdsJson =
          sharedPreferences.getString('onboardedUserIds');
      List<String> onboardedUserIds = [];
      if (onboardedUserIdsJson != null) {
        onboardedUserIds = List<String>.from(json.decode(onboardedUserIdsJson));
      }

      // Check if the current user's ID is already in the list, if not, add it
      if (!onboardedUserIds.contains(user.uid)) {
        onboardedUserIds.add(user.uid);
        // Save the updated list back to SharedPreferences
        await sharedPreferences.setString(
            'onboardedUserIds', json.encode(onboardedUserIds));
      }

      await sharedPreferences.setBool('isOnboarded',
          true); // This line can be removed if you decide to solely rely on the list for onboarding status
      return const Do.success(unit);
    } on FirebaseException catch (e) {
      return Do.failure(AccountManagementFailure.accountDetailsUpdateFailure(
          e.message ?? ''));
    }
  }

  // Helper method to create initial period tracking data based on onboarding information
  Future<void> _createInitialPeriodTrackingData(
      String userId, DateTime lastPeriodDate, int periodLength) async {
    try {
      debugPrint('🩸 Creating initial period tracking data for user $userId');
      debugPrint(
          '🩸 Last period date: $lastPeriodDate, Period length: $periodLength');

      final batch = _firestore.batch();

      // Normalize the last period date to midnight
      final normalizedLastPeriodDate = DateTime(
          lastPeriodDate.year, lastPeriodDate.month, lastPeriodDate.day);

      // Create period tracking entries for the last period using the new structure
      final periodDates = <DateTime>{};

      for (int i = 0; i < periodLength; i++) {
        final periodDay = normalizedLastPeriodDate.add(Duration(days: i));

        // Create entries for all period dates (past, present, and future within period length)
        // This ensures that if user selects today as last period start, all period days are created
        periodDates.add(periodDay);
      }

      if (periodDates.isNotEmpty) {
        debugPrint('🩸 Creating period dates: $periodDates');

        // Group dates by month for efficient updates using new structure
        final Map<String, Map<String, Set<String>>> monthlyUpdates = {};

        for (final date in periodDates) {
          final year = date.year.toString();
          final monthKey =
              '${date.year}_${date.month.toString().padLeft(2, '0')}';
          final dayKey = date.day.toString().padLeft(2, '0');

          monthlyUpdates[year] ??= {};
          monthlyUpdates[year]![monthKey] ??= {};
          monthlyUpdates[year]![monthKey]!.add(dayKey);
        }

        // Update each month document using the new structure
        for (final yearEntry in monthlyUpdates.entries) {
          final year = yearEntry.key;

          for (final monthEntry in yearEntry.value.entries) {
            final monthKey = monthEntry.key;
            final dayKeys = monthEntry.value;

            final monthDocRef = _firestore
                .collection('period_tracking')
                .doc(userId)
                .collection('years')
                .doc(year)
                .collection('months')
                .doc(monthKey);

            // Create the complete days structure
            final updatedDays = <String, dynamic>{};

            for (final dayKey in dayKeys) {
              final dayNum = int.parse(dayKey);
              final monthParts = monthKey.split('_');
              final yearNum = int.parse(monthParts[0]);
              final monthNum = int.parse(monthParts[1]);

              // Create period tracking data with the new structure
              updatedDays[dayKey] = {
                'isPeriodDate': true,
                'lastUpdated': FieldValue.serverTimestamp(),
                'date': Timestamp.fromDate(DateTime(yearNum, monthNum, dayNum)),
                // Initialize other fields as needed
                'symptoms': <dynamic>[],
                'painLevel': null,
                'flowLevel': null,
                'isOvulationDate': false,
              };
            }

            batch.set(
              monthDocRef,
              {'days': updatedDays},
              SetOptions(merge: true),
            );
          }
        }

        // Commit the batch for period dates
        await batch.commit();
        debugPrint('🩸 Successfully created initial period tracking data');

        // Now calculate and save ovulation dates using the period tracking facade
        await _calculateInitialOvulationDates(
            periodDates, lastPeriodDate, periodLength);
      }
    } catch (e) {
      debugPrint('🩸 Error creating initial period tracking data: $e');
      // Don't throw error as this is not critical for onboarding completion
    }
  }

  // Helper method to calculate initial ovulation dates during onboarding
  Future<void> _calculateInitialOvulationDates(Set<DateTime> periodDates,
      DateTime lastPeriodDate, int periodLength) async {
    try {
      debugPrint('🥚 Calculating initial ovulation dates for onboarding');
      debugPrint('🥚 Period dates: $periodDates');

      if (periodDates.isEmpty) {
        debugPrint('🥚 No period dates to calculate ovulation for');
        return;
      }

      // Use the ovulation facade to handle period tracking changes
      // This will calculate and save the ovulation dates for the initial period data
      final result = await _ovulationFacade.handlePeriodTrackingChanges(
        newlySelected: periodDates,
        newlyDeselected: <DateTime>{},
        allPeriodDates: periodDates,
      );

      result.fold(
        onFailure: (failure) {
          debugPrint(
              '🥚 Failed to calculate initial ovulation dates: $failure');
        },
        onSuccess: (_) {
          debugPrint(
              '🥚 Successfully calculated and saved initial ovulation dates');
        },
      );
    } catch (e) {
      debugPrint('🥚 Error calculating initial ovulation dates: $e');
      // Don't throw error as this is not critical for onboarding completion
    }
  }

  // Helper method to initialize predictions from onboarding data
  Future<void> _initializePredictionsFromOnboarding(
    DateTime lastPeriodDate,
    int cycleLength,
    int periodLength,
  ) async {
    try {
      debugPrint('🔮 Initializing predictions from onboarding data');

      // Use the injected period tracking facade for prediction initialization
      final result =
          await _periodTrackingFacade.initializePredictionsFromOnboarding(
        lastPeriodDate,
        cycleLength,
        periodLength,
      );

      result.fold(
        onFailure: (failure) {
          debugPrint(
              '🔮 Failed to initialize predictions from onboarding: $failure');
        },
        onSuccess: (_) {
          debugPrint(
              '🔮 Successfully initialized predictions from onboarding data');
        },
      );
    } catch (e) {
      debugPrint('🔮 Error initializing predictions from onboarding: $e');
      // Don't throw error as this is not critical for onboarding completion
    }
  }
}
